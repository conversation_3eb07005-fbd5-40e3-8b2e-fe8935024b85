from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income.charge.filters import IncomeChargeDetailFieldsFilter
from income.charge.models import IncomeChargeDetail
from income.contrib.drf.views import GenericViewSet
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .serializers import PostPaidBatchInvoiceIssuanceSerializer
from .serializers import PostPaidInvoiceSerializer


@extend_schema_view(
    list=extend_schema(summary="获取待开票的权责信息(后付费开票)"),
    batch_issuance=extend_schema(summary="批量开票"),
)
@extend_schema(tags=["post-paid-invoice"])
class PostPaidInvoiceViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """待开票的权责信息"""

    serializer_class = PostPaidInvoiceSerializer
    serializers = {
        "batch_issuance": PostPaidBatchInvoiceIssuanceSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [IncomeChargeDetailFieldsFilter]
    search_fields = ["sub_order_no", "account_seq", "charge_month", "customer_name"]
    search_contains = True
    table_mapping = {
        "sub_order_no": "a",
        "account_seq": "a",
        "charge_month": "a",
        "customer_name": "b",
    }

    identify = const.MenuIdentify.INVOICE

    def get_queryset(self):
        """
        使用Raw SQL获取完整数据, 减少内存使用
        """
        return self._get_raw_queryset()

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def _get_raw_queryset(self):
        """使用Raw SQL获取完整数据"""
        sql = """
        SELECT
            a.`id`,
            a.`sub_order_no`,
            a.`account_seq`,
            a.`income_type`,
            a.`pay_type`,
            a.`tax`,
            a.`charge_month`,
            a.`fee_amount`,
            b.`customer_name`,
            c.`adjust_month`,
            COALESCE(d.`invoice_amount`, 0) AS invoice_amount
        FROM
            `income_charge_detail` a
            LEFT JOIN `customer_info` b ON a. `customer_num` = b. `customer_num`
            LEFT JOIN `income_adjust_detail` c ON a. `income_adjust_id` = c.id
            LEFT JOIN (
                SELECT
                    `target_id`,
                    sum(`amount`) AS invoice_amount
                FROM
                    `income_invoice_detail`
                WHERE
                    target_type = 'bill'
                GROUP BY
                    `target_id`) d ON a.id = d.`target_id`
        WHERE
            a.`pay_type` = '预付'
            AND (d.invoice_amount IS NULL OR a.`fee_amount` != d.invoice_amount)
            AND a.`need_invoice` = 1
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql)

    @action(
        methods=["post"],
        detail=False,
        url_path="batch-issuance",
    )
    def batch_issuance(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        # TODO: 发送MQ消息
        return Response({"message": "批量开票成功"})
