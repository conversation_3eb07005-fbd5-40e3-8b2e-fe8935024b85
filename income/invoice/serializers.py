from collections import defaultdict
from decimal import Decimal
from django.db import transaction
from rest_framework import serializers

from income import message
from income.charge.models import IncomeChargeDetail
from income.customer.models import IncomeAccountSeq
from income.invoice.models import IncomeInvoice, IncomeInvoiceDetail


class PostPaidInvoiceSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(read_only=True, help_text="客户名称")
    adjust_month = serializers.IntegerField(read_only=True, help_text="调整账期")
    invoice_amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        read_only=True,
        help_text="开票金额",
    )

    class Meta:
        model = IncomeChargeDetail
        fields = (
            "id",
            "sub_order_no",
            "account_seq",
            "charge_month",
            "fee_amount",
            "tax",
            "tax_type",
            "income_type",
            "pay_type",
            "customer_name",
            "adjust_month",
            "invoice_amount",
        )


class PostPaidBatchInvoiceIssuanceSerializer(serializers.Serializer):
    # 客户ID和分账序号可不选择
    customer_id = serializers.IntegerField(help_text="客户id", required=False)
    account_seq = serializers.CharField(help_text="分账序号", required=False)
    # 账期区间, 必填
    start_charge_month = serializers.IntegerField(help_text="起始账期", required=True)
    end_charge_month = serializers.IntegerField(help_text="结束账期", required=True)

    def validate(self, attrs):
        if attrs["start_charge_month"] > attrs["end_charge_month"]:
            raise serializers.ValidationError(
                {"start_charge_month": message.INVALID_CHARGE_MONTH_RANGE},
            )
        return attrs

    def save(self, **kwargs):
        """批量开票逻辑实现"""
        with transaction.atomic():
            # 1. 获取当前的所有数据（list接口的数据，需先进行filterbackend的筛选）
            filtered_data = self._get_filtered_data()

            # 2. 按照分账序号分组，累加明细数据金额（fee_amount-pay_amount）
            grouped_data = self._group_by_account_seq(filtered_data)

            # 3. 操作入表income_invoice表
            # 4. 操作入表income_invoice_detail表
            self._create_invoices_and_details(grouped_data)

    def _get_filtered_data(self):
        """获取筛选后的数据"""
        from income.invoice.views import PostPaidInvoiceViewSet

        # 创建一个模拟的ViewSet实例来获取数据
        viewset = PostPaidInvoiceViewSet()

        # 获取原始查询集
        queryset = viewset.get_queryset()

        # 手动筛选数据，因为需要处理账期区间和其他条件
        filtered_data = []
        start_month = self.validated_data['start_charge_month']
        end_month = self.validated_data['end_charge_month']
        customer_id = self.validated_data.get('customer_id')
        account_seq = self.validated_data.get('account_seq')

        for item in queryset:
            # 账期筛选
            if not (start_month <= item.charge_month <= end_month):
                continue

            # 分账序号筛选
            if account_seq and item.account_seq != account_seq:
                continue

            # 客户ID筛选 (这里需要根据实际的客户ID字段进行筛选)
            # 由于原始SQL中没有直接的customer_id字段，这里暂时跳过

            filtered_data.append(item)

        return filtered_data

    def _group_by_account_seq(self, data):
        """按照分账序号分组，累加明细数据金额"""
        grouped = defaultdict(list)

        for item in data:
            # 计算每条记录的可开票金额（fee_amount - 已开票金额）
            fee_amount = item.fee_amount or Decimal('0')
            invoice_amount = getattr(item, 'invoice_amount', 0) or Decimal('0')
            available_amount = fee_amount - invoice_amount

            # 只处理有可开票金额的记录
            if available_amount > 0:
                grouped[item.account_seq].append({
                    'id': item.id,
                    'available_amount': available_amount,
                    'currency_type': item.currency_type,
                })

        return grouped

    def _create_invoices_and_details(self, grouped_data):
        """创建发票和发票明细"""
        for account_seq, items in grouped_data.items():
            # 计算该分账序号的总金额
            total_amount = sum(item['available_amount'] for item in items)

            # 获取分账序号对应的税率
            try:
                account_seq_obj = IncomeAccountSeq.objects.get(account_seq=account_seq)
                tax_rate = account_seq_obj.tax
            except IncomeAccountSeq.DoesNotExist:
                tax_rate = 0

            # 随机挑选一条权责的currency_type
            currency_type = items[0]['currency_type'] if items else None

            # 计算税额：tax_amount = amount - (amount / (1 + tax/100))
            if tax_rate > 0:
                tax_amount = total_amount - (total_amount / (1 + Decimal(tax_rate) / 100))
            else:
                tax_amount = Decimal('0')

            # 创建发票记录
            invoice = IncomeInvoice.objects.create(
                invoice_no=self._generate_invoice_no(),
                amount=total_amount,
                account_seq=account_seq,
                tax_rate=tax_rate,
                tax_amount=tax_amount,
                currency_type=currency_type,
                invoice_currency_type=currency_type,
                exchange_rate=None,  # 置空
            )

            # 创建发票明细记录
            for item in items:
                IncomeInvoiceDetail.objects.create(
                    invoice_id=invoice.id,
                    target_id=item['id'],
                    target_type='bill',
                    amount=item['available_amount'],
                    invoice_month=None,  # 置空
                )

    def _generate_invoice_no(self):
        """生成发票号"""
        import time
        from datetime import datetime

        # 生成格式：INV + 年月日 + 时间戳后6位
        now = datetime.now()
        date_str = now.strftime('%Y%m%d')
        timestamp_suffix = str(int(time.time()))[-6:]
        return f"INV{date_str}{timestamp_suffix}"
