# ruff: noqa: INP001
import logging

from django.conf import settings

from pkg.amqp import ConsumerConfig
from pkg.amqp import MQWorker

logger = logging.getLogger("datasource")


@MQWorker(
    consumer_config=ConsumerConfig(queue=settings.SP_QUEUE, prefetch_count=1),
    logger_=logger,
)
def main(data):
    """
    系统对接队列
    {
        "uuid": "6dcf2460-a3df-11eb-9f1e-acde48001122",  # 消息的唯一标识
        "email": "<EMAIL>",
        "name": "sp_test1",  # 名称
        "s3path": "hcs/sp/********/customer.csv",  # 文件的s3路径
        "identifyColumns": ["phone", "email"],  # 客户身份字段
        "attributeColumns": ["age", "gender"],  # 客户属性字段
        "millis": "*************"  # 时间戳
    }
    """
    ...

"""
def mq_push_data(push_data_list: list):
    with <PERSON><PERSON><PERSON>or<PERSON>(settings.RABBITMQ) as mq_worker:
        for push_data in push_data_list:
            mq_worker.publish(
                body=push_data,
                routing_key=settings.HCS_WORKFLOW_QUEUE,
            )
"""
